# GC Monitor for Chatwoot
# Logs GC statistics and provides endpoints for manual GC

if Rails.env.development?
  # Enable GC profiling
  GC::Profiler.enable
  
  # Log GC stats periodically
  Thread.new do
    loop do
      sleep 300  # Every 5 minutes
      gc_stats = GC.stat
      Rails.logger.info "[GC Stats] Count: #{gc_stats[:count]}, Heap slots: #{gc_stats[:heap_live_slots]}, Memory: #{`ps -o rss= -p #{Process.pid}`.to_i / 1024}MB"
      
      # Force GC if memory is high
      memory_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024
      if memory_mb > (Rails.application.class.name.include?('Sidekiq') ? 250 : 350)
        Rails.logger.info "[GC] Triggering garbage collection due to high memory usage: #{memory_mb}MB"
        GC.start
      end
    end
  end
  
  # Handle USR1 signal for manual GC
  Signal.trap('USR1') do
    Rails.logger.info "[GC] Manual garbage collection triggered"
    before_memory = `ps -o rss= -p #{Process.pid}`.to_i / 1024
    GC.start
    after_memory = `ps -o rss= -p #{Process.pid}`.to_i / 1024
    reduction = before_memory - after_memory
    Rails.logger.info "[GC] Memory before: #{before_memory}MB, after: #{after_memory}MB, reduced: #{reduction}MB"
  end
end

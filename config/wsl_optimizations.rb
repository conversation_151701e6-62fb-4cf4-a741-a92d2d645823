# WSL2 Performance Optimizations for Chatwoot
# This file contains optimizations specifically for running Chatwoot on WSL2

# Detect if running on WSL
def running_on_wsl?
  File.exist?('/proc/version') && File.read('/proc/version').include?('microsoft')
end

if running_on_wsl?
  # Optimize file watching for WSL2
  Rails.application.configure do
    # Use polling instead of inotify for file watching in WSL2
    config.file_watcher = ActiveSupport::FileUpdate<PERSON>he<PERSON>
    
    # Reduce the frequency of file system checks
    config.reload_classes_only_on_change = true
    
    # Optimize asset serving
    config.public_file_server.enabled = true
    config.public_file_server.headers = {
      'Cache-Control' => 'public, max-age=31536000'
    }
  end
  
  # Memory optimization for WSL2
  if defined?(GC)
    # Tune garbage collection for memory-constrained environment
    GC::Profiler.enable
    
    # More aggressive garbage collection
    ENV['RUBY_GC_HEAP_INIT_SLOTS'] ||= '10000'
    ENV['RUBY_GC_HEAP_FREE_SLOTS'] ||= '1000'
    ENV['RUBY_GC_HEAP_GROWTH_FACTOR'] ||= '1.1'
    ENV['RUBY_GC_HEAP_GROWTH_MAX_SLOTS'] ||= '10000'
    ENV['RUBY_GC_MALLOC_LIMIT'] ||= '16000000'
    ENV['RUBY_GC_MALLOC_LIMIT_MAX'] ||= '32000000'
    ENV['RUBY_GC_MALLOC_LIMIT_GROWTH_FACTOR'] ||= '1.1'
    ENV['RUBY_GC_OLDMALLOC_LIMIT'] ||= '16000000'
    ENV['RUBY_GC_OLDMALLOC_LIMIT_MAX'] ||= '128000000'
    ENV['RUBY_GC_OLDMALLOC_LIMIT_GROWTH_FACTOR'] ||= '1.2'
  end
  
  puts "[WSL2 Optimizations] Applied WSL2-specific performance optimizations"
end

#!/usr/bin/env bash

# Real-time Memory Monitor for Chatwoot
# Monitors memory usage patterns and identifies memory leaks

LOG_FILE="logs/memory_monitor.log"
INTERVAL=${1:-5}  # Default 5 seconds
DURATION=${2:-300}  # Default 5 minutes

# Create logs directory if it doesn't exist
mkdir -p logs

echo "=== Chatwoot Real-time Memory Monitor ===" | tee -a "$LOG_FILE"
echo "Monitoring interval: ${INTERVAL}s, Duration: ${DURATION}s" | tee -a "$LOG_FILE"
echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"
echo "Started at: $(date)" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Function to get process memory
get_process_memory() {
    local process_name="$1"
    ps aux | grep -E "$process_name" | grep -v grep | awk '{sum+=$6} END {printf "%.0f", sum/1024}'
}

# Function to get specific PID memory
get_pid_memory() {
    local pid="$1"
    ps -p "$pid" -o rss= 2>/dev/null | awk '{printf "%.0f", $1/1024}' || echo "0"
}

# Get initial PIDs
VITE_PID=$(pgrep -f "vite.*development" | head -1)
PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)

echo "Monitoring PIDs:" | tee -a "$LOG_FILE"
echo "Vite: $VITE_PID" | tee -a "$LOG_FILE"
echo "Puma: $PUMA_PID" | tee -a "$LOG_FILE"
echo "Sidekiq: $SIDEKIQ_PID" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Header
printf "%-19s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s\n" \
    "Time" "Total" "Avail" "Swap%" "Vite" "Puma" "Sidekiq" "VSCode" "Load" | tee -a "$LOG_FILE"
printf "%-19s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s\n" \
    "---" "---" "---" "---" "---" "---" "---" "---" "---" | tee -a "$LOG_FILE"

# Monitoring loop
END_TIME=$(($(date +%s) + DURATION))
while [ $(date +%s) -lt $END_TIME ]; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    
    # System memory
    MEMORY_INFO=$(free -m | awk 'NR==2{printf "%.0f %.0f", $2, $7}')
    TOTAL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f1)
    AVAIL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f2)
    
    # Swap usage
    SWAP_USED=$(free | awk 'NR==3{if($2>0) printf "%.0f", $3/$2*100; else print "0"}')
    
    # Process memory
    VITE_MEM=$(get_pid_memory "$VITE_PID")
    PUMA_MEM=$(get_pid_memory "$PUMA_PID")
    SIDEKIQ_MEM=$(get_pid_memory "$SIDEKIQ_PID")
    VSCODE_MEM=$(get_process_memory "vscode-server")
    
    # Load average
    LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    # Output
    printf "%-19s %-8s %-8s %-8s %-8s %-8s %-8s %-8s %-8s\n" \
        "$TIMESTAMP" "${TOTAL_MEM}M" "${AVAIL_MEM}M" "${SWAP_USED}%" \
        "${VITE_MEM}M" "${PUMA_MEM}M" "${SIDEKIQ_MEM}M" "${VSCODE_MEM}M" "$LOAD" | tee -a "$LOG_FILE"
    
    # Check for memory leaks
    if [ "$VITE_MEM" -gt 1000 ]; then
        echo "⚠️  ALERT: Vite memory usage high: ${VITE_MEM}MB" | tee -a "$LOG_FILE"
    fi
    
    if [ "$AVAIL_MEM" -lt 200 ]; then
        echo "⚠️  ALERT: Low available memory: ${AVAIL_MEM}MB" | tee -a "$LOG_FILE"
    fi
    
    if [ "$SWAP_USED" -gt 80 ]; then
        echo "⚠️  ALERT: High swap usage: ${SWAP_USED}%" | tee -a "$LOG_FILE"
    fi
    
    sleep "$INTERVAL"
done

echo "" | tee -a "$LOG_FILE"
echo "Monitoring completed at: $(date)" | tee -a "$LOG_FILE"

# Generate summary
echo "=== Memory Usage Summary ===" | tee -a "$LOG_FILE"
echo "Peak Vite Memory: $(grep -o 'Vite.*M' "$LOG_FILE" | grep -o '[0-9]*' | sort -n | tail -1)MB" | tee -a "$LOG_FILE"
echo "Peak Puma Memory: $(grep -o 'Puma.*M' "$LOG_FILE" | grep -o '[0-9]*' | sort -n | tail -1)MB" | tee -a "$LOG_FILE"
echo "Peak Sidekiq Memory: $(grep -o 'Sidekiq.*M' "$LOG_FILE" | grep -o '[0-9]*' | sort -n | tail -1)MB" | tee -a "$LOG_FILE"
echo "Minimum Available Memory: $(grep -o 'Avail.*M' "$LOG_FILE" | grep -o '[0-9]*' | sort -n | head -1)MB" | tee -a "$LOG_FILE"

#!/usr/bin/env bash

# Process Manager for Chatwoot
# Monitors and manages memory usage, restarts processes when needed

MEMORY_THRESHOLD_VITE=800    # MB
MEMORY_THRESHOLD_PUMA=400    # MB
MEMORY_THRESHOLD_SIDEKIQ=300 # MB
CHECK_INTERVAL=30            # seconds
LOG_FILE="logs/process_manager.log"

# Create logs directory
mkdir -p logs

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

get_process_memory() {
    local pid="$1"
    ps -p "$pid" -o rss= 2>/dev/null | awk '{printf "%.0f", $1/1024}' || echo "0"
}

restart_vite() {
    log "⚠️  Restarting Vite due to high memory usage"
    
    # Find and kill Vite processes
    pkill -f "vite.*development"
    sleep 2
    
    # Restart via overmind
    overmind restart vite 2>/dev/null || {
        log "❌ Failed to restart Vite via overmind"
        return 1
    }
    
    log "✅ Vite restarted successfully"
}

restart_puma() {
    log "⚠️  Restarting Puma due to high memory usage"
    
    # Graceful restart
    overmind restart backend 2>/dev/null || {
        log "❌ Failed to restart Puma via overmind"
        return 1
    }
    
    log "✅ Puma restarted successfully"
}

restart_sidekiq() {
    log "⚠️  Restarting Sidekiq due to high memory usage"
    
    # Graceful restart
    overmind restart worker 2>/dev/null || {
        log "❌ Failed to restart Sidekiq via overmind"
        return 1
    }
    
    log "✅ Sidekiq restarted successfully"
}

check_system_memory() {
    local available_mem=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    local swap_used=$(free | awk 'NR==3{if($2>0) printf "%.0f", $3/$2*100; else print "0"}')
    
    if [ "$available_mem" -lt 200 ]; then
        log "🚨 CRITICAL: Low system memory: ${available_mem}MB available"
        
        # Force garbage collection
        log "🔄 Triggering garbage collection..."
        pkill -USR1 -f "puma.*chatwoot" 2>/dev/null
        pkill -USR1 -f "sidekiq.*chatwoot" 2>/dev/null
        
        return 1
    fi
    
    if [ "$swap_used" -gt 90 ]; then
        log "🚨 CRITICAL: High swap usage: ${swap_used}%"
        return 1
    fi
    
    return 0
}

monitor_processes() {
    log "🔍 Starting process monitoring..."
    
    while true; do
        # Get current PIDs
        VITE_PID=$(pgrep -f "vite.*development" | head -1)
        PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
        SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
        
        # Check system memory first
        if ! check_system_memory; then
            log "⚠️  System memory critical, waiting before process checks..."
            sleep 60
            continue
        fi
        
        # Check Vite memory
        if [ -n "$VITE_PID" ]; then
            VITE_MEM=$(get_process_memory "$VITE_PID")
            if [ "$VITE_MEM" -gt "$MEMORY_THRESHOLD_VITE" ]; then
                log "⚠️  Vite memory usage high: ${VITE_MEM}MB (threshold: ${MEMORY_THRESHOLD_VITE}MB)"
                restart_vite
                sleep 30  # Wait for restart
            fi
        fi
        
        # Check Puma memory
        if [ -n "$PUMA_PID" ]; then
            PUMA_MEM=$(get_process_memory "$PUMA_PID")
            if [ "$PUMA_MEM" -gt "$MEMORY_THRESHOLD_PUMA" ]; then
                log "⚠️  Puma memory usage high: ${PUMA_MEM}MB (threshold: ${MEMORY_THRESHOLD_PUMA}MB)"
                restart_puma
                sleep 30  # Wait for restart
            fi
        fi
        
        # Check Sidekiq memory
        if [ -n "$SIDEKIQ_PID" ]; then
            SIDEKIQ_MEM=$(get_process_memory "$SIDEKIQ_PID")
            if [ "$SIDEKIQ_MEM" -gt "$MEMORY_THRESHOLD_SIDEKIQ" ]; then
                log "⚠️  Sidekiq memory usage high: ${SIDEKIQ_MEM}MB (threshold: ${MEMORY_THRESHOLD_SIDEKIQ}MB)"
                restart_sidekiq
                sleep 30  # Wait for restart
            fi
        fi
        
        # Log current status
        log "📊 Memory status - Vite: ${VITE_MEM:-0}MB, Puma: ${PUMA_MEM:-0}MB, Sidekiq: ${SIDEKIQ_MEM:-0}MB"
        
        sleep "$CHECK_INTERVAL"
    done
}

# Handle signals
trap 'log "🛑 Process manager stopped"; exit 0' SIGTERM SIGINT

# Start monitoring
log "🚀 Process manager started with thresholds - Vite: ${MEMORY_THRESHOLD_VITE}MB, Puma: ${MEMORY_THRESHOLD_PUMA}MB, Sidekiq: ${MEMORY_THRESHOLD_SIDEKIQ}MB"
monitor_processes

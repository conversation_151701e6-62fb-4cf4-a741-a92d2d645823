#!/usr/bin/env bash

# Chatwoot Performance Monitor for WSL2
# This script monitors system resources and provides optimization recommendations

echo "=== Chatwoot Performance Monitor ==="
echo "Timestamp: $(date)"
echo ""

# Check memory usage
echo "=== Memory Usage ==="
free -h
echo ""

# Check swap usage
echo "=== Swap Usage ==="
swapon --show
echo ""

# Check disk usage
echo "=== Disk Usage ==="
df -h /
echo ""

# Check Redis status
echo "=== Redis Status ==="
redis-cli ping 2>/dev/null && echo "Redis: OK" || echo "Redis: NOT RESPONDING"
echo ""

# Check PostgreSQL status
echo "=== PostgreSQL Status ==="
systemctl is-active postgresql@16-main 2>/dev/null && echo "PostgreSQL: OK" || echo "PostgreSQL: NOT RUNNING"
echo ""

# Check running processes
echo "=== Top Memory Consumers ==="
ps aux --sort=-%mem | head -10
echo ""

# Check load average
echo "=== System Load ==="
uptime
echo ""

# Performance recommendations
echo "=== Performance Recommendations ==="

# Check available memory
AVAILABLE_MEM=$(free -m | awk 'NR==2{printf "%.0f", $7}')
if [ "$AVAILABLE_MEM" -lt 500 ]; then
    echo "⚠️  WARNING: Low available memory ($AVAILABLE_MEM MB)"
    echo "   Recommendation: Consider increasing WSL2 memory allocation"
    echo "   Add to .wslconfig: memory=4GB"
fi

# Check swap usage
SWAP_USED=$(free | awk 'NR==3{printf "%.0f", $3/$2*100}')
if [ "$SWAP_USED" -gt 50 ]; then
    echo "⚠️  WARNING: High swap usage ($SWAP_USED%)"
    echo "   Recommendation: Increase RAM or optimize application memory usage"
fi

# Check if caching is enabled
if [ -f "tmp/caching-dev.txt" ]; then
    echo "✅ Development caching is enabled"
else
    echo "⚠️  Development caching is disabled"
    echo "   Recommendation: Run 'rails dev:cache' to enable caching"
fi

echo ""
echo "=== End of Performance Report ==="

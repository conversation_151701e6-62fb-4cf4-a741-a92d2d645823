#!/usr/bin/env bash

# Performance Validator for Chatwoot
# Comprehensive testing and validation of performance optimizations

LOG_FILE="logs/performance_validation.log"
mkdir -p logs

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

# Function to test response times
test_response_times() {
    log "🚀 Testing response times..."
    
    # Test backend response
    BACKEND_TIME=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:3000)
    BACKEND_MS=$(echo "$BACKEND_TIME * 1000" | bc -l | cut -d. -f1)
    
    if [ "$BACKEND_MS" -lt 2000 ]; then
        success "Backend response time: ${BACKEND_MS}ms (excellent)"
    elif [ "$BACKEND_MS" -lt 5000 ]; then
        warning "Backend response time: ${BACKEND_MS}ms (acceptable)"
    else
        error "Backend response time: ${BACKEND_MS}ms (too slow)"
    fi
    
    # Test Vite response
    VITE_TIME=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:3036/vite-dev/)
    VITE_MS=$(echo "$VITE_TIME * 1000" | bc -l | cut -d. -f1)
    
    if [ "$VITE_MS" -lt 100 ]; then
        success "Vite response time: ${VITE_MS}ms (excellent)"
    elif [ "$VITE_MS" -lt 500 ]; then
        warning "Vite response time: ${VITE_MS}ms (acceptable)"
    else
        error "Vite response time: ${VITE_MS}ms (too slow)"
    fi
}

# Function to test memory usage
test_memory_usage() {
    log "🧠 Testing memory usage..."
    
    # System memory
    MEMORY_INFO=$(free -m | awk 'NR==2{printf "%.0f %.0f", $2, $7}')
    TOTAL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f1)
    AVAIL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f2)
    
    if [ "$AVAIL_MEM" -gt 500 ]; then
        success "Available memory: ${AVAIL_MEM}MB (healthy)"
    elif [ "$AVAIL_MEM" -gt 200 ]; then
        warning "Available memory: ${AVAIL_MEM}MB (low)"
    else
        error "Available memory: ${AVAIL_MEM}MB (critical)"
    fi
    
    # Swap usage
    SWAP_USED=$(free | awk 'NR==3{if($2>0) printf "%.0f", $3/$2*100; else print "0"}')
    
    if [ "$SWAP_USED" -lt 30 ]; then
        success "Swap usage: ${SWAP_USED}% (healthy)"
    elif [ "$SWAP_USED" -lt 70 ]; then
        warning "Swap usage: ${SWAP_USED}% (moderate)"
    else
        error "Swap usage: ${SWAP_USED}% (high)"
    fi
    
    # Process memory
    VITE_PID=$(pgrep -f "vite.*development" | head -1)
    PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
    SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
    
    if [ -n "$VITE_PID" ]; then
        VITE_MEM=$(ps -p "$VITE_PID" -o rss= | awk '{printf "%.0f", $1/1024}')
        if [ "$VITE_MEM" -lt 500 ]; then
            success "Vite memory: ${VITE_MEM}MB (healthy)"
        else
            warning "Vite memory: ${VITE_MEM}MB (high)"
        fi
    else
        error "Vite process not found"
    fi
    
    if [ -n "$PUMA_PID" ]; then
        PUMA_MEM=$(ps -p "$PUMA_PID" -o rss= | awk '{printf "%.0f", $1/1024}')
        if [ "$PUMA_MEM" -lt 300 ]; then
            success "Puma memory: ${PUMA_MEM}MB (healthy)"
        else
            warning "Puma memory: ${PUMA_MEM}MB (high)"
        fi
    else
        error "Puma process not found"
    fi
    
    if [ -n "$SIDEKIQ_PID" ]; then
        SIDEKIQ_MEM=$(ps -p "$SIDEKIQ_PID" -o rss= | awk '{printf "%.0f", $1/1024}')
        if [ "$SIDEKIQ_MEM" -lt 250 ]; then
            success "Sidekiq memory: ${SIDEKIQ_MEM}MB (healthy)"
        else
            warning "Sidekiq memory: ${SIDEKIQ_MEM}MB (high)"
        fi
    else
        error "Sidekiq process not found"
    fi
}

# Function to test service connectivity
test_service_connectivity() {
    log "🔗 Testing service connectivity..."
    
    # Redis
    if redis-cli ping > /dev/null 2>&1; then
        success "Redis connection: OK"
    else
        error "Redis connection: Failed"
    fi
    
    # PostgreSQL
    if psql -h localhost -U cirovas -d chatwoot_development -c "SELECT 1;" > /dev/null 2>&1; then
        success "PostgreSQL connection: OK"
    else
        error "PostgreSQL connection: Failed"
    fi
    
    # Backend HTTP
    if curl -s http://localhost:3000 > /dev/null; then
        success "Backend HTTP: OK"
    else
        error "Backend HTTP: Failed"
    fi
    
    # Vite HTTP
    if curl -s http://localhost:3036/vite-dev/ > /dev/null; then
        success "Vite HTTP: OK"
    else
        error "Vite HTTP: Failed"
    fi
}

# Function to test frontend loading
test_frontend_loading() {
    log "🌐 Testing frontend loading..."
    
    # Check if HTML contains expected elements
    HTML_CONTENT=$(curl -s http://localhost:3000)
    
    if echo "$HTML_CONTENT" | grep -q "Chatwoot"; then
        success "HTML title: Found"
    else
        error "HTML title: Missing"
    fi
    
    if echo "$HTML_CONTENT" | grep -q "vite"; then
        success "Vite integration: Found"
    else
        error "Vite integration: Missing"
    fi
    
    # Check for JavaScript errors (basic check)
    if echo "$HTML_CONTENT" | grep -q "script"; then
        success "JavaScript includes: Found"
    else
        error "JavaScript includes: Missing"
    fi
}

# Function to run load test
run_load_test() {
    log "⚡ Running load test..."
    
    info "Sending 10 concurrent requests..."
    
    # Create temporary file for results
    TEMP_FILE=$(mktemp)
    
    # Run 10 concurrent requests
    for i in {1..10}; do
        (curl -w "%{time_total}\n" -o /dev/null -s http://localhost:3000 >> "$TEMP_FILE") &
    done
    
    # Wait for all requests to complete
    wait
    
    # Calculate statistics
    TOTAL_TIME=$(awk '{sum+=$1} END {printf "%.3f", sum}' "$TEMP_FILE")
    AVG_TIME=$(awk '{sum+=$1} END {printf "%.3f", sum/NR}' "$TEMP_FILE")
    MAX_TIME=$(awk 'BEGIN{max=0} {if($1>max) max=$1} END {printf "%.3f", max}' "$TEMP_FILE")
    MIN_TIME=$(awk 'BEGIN{min=999} {if($1<min) min=$1} END {printf "%.3f", min}' "$TEMP_FILE")
    
    info "Load test results:"
    info "  Total time: ${TOTAL_TIME}s"
    info "  Average time: ${AVG_TIME}s"
    info "  Min time: ${MIN_TIME}s"
    info "  Max time: ${MAX_TIME}s"
    
    # Evaluate results
    AVG_MS=$(echo "$AVG_TIME * 1000" | bc -l | cut -d. -f1)
    if [ "$AVG_MS" -lt 3000 ]; then
        success "Load test: Average response time ${AVG_MS}ms (good)"
    else
        warning "Load test: Average response time ${AVG_MS}ms (needs improvement)"
    fi
    
    rm -f "$TEMP_FILE"
}

# Function to generate performance report
generate_report() {
    log "📊 Generating performance report..."
    
    REPORT_FILE="logs/performance_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$REPORT_FILE" << EOF
# Chatwoot Performance Validation Report

**Generated:** $(date)

## System Overview
- **Total Memory:** $(free -h | awk 'NR==2{print $2}')
- **Available Memory:** $(free -h | awk 'NR==2{print $7}')
- **Swap Usage:** $(free | awk 'NR==3{if($2>0) printf "%.1f%%", $3/$2*100; else print "0%"}')
- **Load Average:** $(uptime | awk -F'load average:' '{print $2}')

## Process Status
$(ps aux --sort=-%mem | head -10 | awk 'BEGIN{print "| Process | PID | Memory | CPU |"} BEGIN{print "|---------|-----|--------|-----|"} NR>1{printf "| %s | %s | %s | %s |\n", $11, $2, $6/1024"MB", $3"%"}')

## Performance Metrics
- **Backend Response Time:** $(curl -w "%{time_total}s" -o /dev/null -s http://localhost:3000 2>/dev/null || echo "N/A")
- **Vite Response Time:** $(curl -w "%{time_total}s" -o /dev/null -s http://localhost:3036/vite-dev/ 2>/dev/null || echo "N/A")

## Recommendations
$(tail -20 "$LOG_FILE" | grep -E "(⚠️|❌)" || echo "No critical issues found")

---
*Report generated by Chatwoot Performance Validator*
EOF
    
    success "Performance report saved to: $REPORT_FILE"
}

# Main validation function
run_validation() {
    log "🔍 Starting comprehensive performance validation..."
    
    test_service_connectivity
    test_memory_usage
    test_response_times
    test_frontend_loading
    run_load_test
    generate_report
    
    log "✅ Performance validation completed"
}

# Main function
main() {
    case "${1:-full}" in
        "full")
            run_validation
            ;;
        "memory")
            test_memory_usage
            ;;
        "response")
            test_response_times
            ;;
        "connectivity")
            test_service_connectivity
            ;;
        "frontend")
            test_frontend_loading
            ;;
        "load")
            run_load_test
            ;;
        "report")
            generate_report
            ;;
        *)
            echo "Usage: $0 {full|memory|response|connectivity|frontend|load|report}"
            exit 1
            ;;
    esac
}

# Start validation
main "$@"

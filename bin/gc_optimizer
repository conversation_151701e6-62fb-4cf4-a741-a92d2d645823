#!/usr/bin/env bash

# Garbage Collection Optimizer for Chatwoot
# Optimizes Ruby GC settings and triggers manual GC when needed

LOG_FILE="logs/gc_optimizer.log"
mkdir -p logs

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to get process memory
get_process_memory() {
    local pid="$1"
    ps -p "$pid" -o rss= 2>/dev/null | awk '{printf "%.0f", $1/1024}' || echo "0"
}

# Function to trigger garbage collection
trigger_gc() {
    local process_name="$1"
    local pid="$2"
    
    log "🗑️  Triggering garbage collection for $process_name (PID: $pid)"
    
    # Send USR1 signal to trigger GC
    kill -USR1 "$pid" 2>/dev/null && {
        log "✅ GC signal sent to $process_name"
        return 0
    } || {
        log "❌ Failed to send GC signal to $process_name"
        return 1
    }
}

# Function to optimize Ruby GC environment variables
optimize_gc_env() {
    log "🔧 Optimizing Ruby GC environment variables"
    
    # Export optimized GC settings
    export RUBY_GC_HEAP_INIT_SLOTS=10000
    export RUBY_GC_HEAP_FREE_SLOTS=1000
    export RUBY_GC_HEAP_GROWTH_FACTOR=1.1
    export RUBY_GC_HEAP_GROWTH_MAX_SLOTS=10000
    export RUBY_GC_MALLOC_LIMIT=16000000
    export RUBY_GC_MALLOC_LIMIT_MAX=32000000
    export RUBY_GC_MALLOC_LIMIT_GROWTH_FACTOR=1.1
    export RUBY_GC_OLDMALLOC_LIMIT=16000000
    export RUBY_GC_OLDMALLOC_LIMIT_MAX=128000000
    export RUBY_GC_OLDMALLOC_LIMIT_GROWTH_FACTOR=1.2
    
    # More aggressive settings for memory-constrained environment
    export RUBY_GC_HEAP_OLDOBJECT_LIMIT_FACTOR=0.9
    export RUBY_GC_HEAP_REMEMBERED_WB_UNPROTECTED_OBJECTS_LIMIT_RATIO=0.01
    
    log "✅ GC environment variables optimized"
}

# Function to monitor and trigger GC based on memory usage
monitor_and_gc() {
    local puma_threshold=350    # MB
    local sidekiq_threshold=250 # MB
    local check_interval=60     # seconds
    
    log "🔍 Starting GC monitoring (Puma: ${puma_threshold}MB, Sidekiq: ${sidekiq_threshold}MB)"
    
    while true; do
        # Get current PIDs
        PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
        SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
        
        # Check Puma memory
        if [ -n "$PUMA_PID" ]; then
            PUMA_MEM=$(get_process_memory "$PUMA_PID")
            if [ "$PUMA_MEM" -gt "$puma_threshold" ]; then
                log "⚠️  Puma memory high: ${PUMA_MEM}MB (threshold: ${puma_threshold}MB)"
                trigger_gc "Puma" "$PUMA_PID"
                sleep 5  # Wait for GC to complete
                
                # Check if memory reduced
                PUMA_MEM_AFTER=$(get_process_memory "$PUMA_PID")
                REDUCTION=$((PUMA_MEM - PUMA_MEM_AFTER))
                log "📊 Puma memory after GC: ${PUMA_MEM_AFTER}MB (reduced by ${REDUCTION}MB)"
            fi
        fi
        
        # Check Sidekiq memory
        if [ -n "$SIDEKIQ_PID" ]; then
            SIDEKIQ_MEM=$(get_process_memory "$SIDEKIQ_PID")
            if [ "$SIDEKIQ_MEM" -gt "$sidekiq_threshold" ]; then
                log "⚠️  Sidekiq memory high: ${SIDEKIQ_MEM}MB (threshold: ${sidekiq_threshold}MB)"
                trigger_gc "Sidekiq" "$SIDEKIQ_PID"
                sleep 5  # Wait for GC to complete
                
                # Check if memory reduced
                SIDEKIQ_MEM_AFTER=$(get_process_memory "$SIDEKIQ_PID")
                REDUCTION=$((SIDEKIQ_MEM - SIDEKIQ_MEM_AFTER))
                log "📊 Sidekiq memory after GC: ${SIDEKIQ_MEM_AFTER}MB (reduced by ${REDUCTION}MB)"
            fi
        fi
        
        sleep "$check_interval"
    done
}

# Function to show GC statistics
show_gc_stats() {
    log "📊 Collecting GC statistics..."
    
    # Get Puma GC stats
    PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
    if [ -n "$PUMA_PID" ]; then
        echo "=== Puma GC Stats ===" >> "$LOG_FILE"
        # This would require a custom endpoint or script to get GC stats
        # For now, just log the memory usage
        PUMA_MEM=$(get_process_memory "$PUMA_PID")
        log "Puma current memory: ${PUMA_MEM}MB"
    fi
    
    # Get Sidekiq GC stats
    SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
    if [ -n "$SIDEKIQ_PID" ]; then
        echo "=== Sidekiq GC Stats ===" >> "$LOG_FILE"
        SIDEKIQ_MEM=$(get_process_memory "$SIDEKIQ_PID")
        log "Sidekiq current memory: ${SIDEKIQ_MEM}MB"
    fi
}

# Function to force GC on all Ruby processes
force_gc_all() {
    log "🗑️  Forcing garbage collection on all Ruby processes"
    
    PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
    SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
    
    if [ -n "$PUMA_PID" ]; then
        trigger_gc "Puma" "$PUMA_PID"
    fi
    
    if [ -n "$SIDEKIQ_PID" ]; then
        trigger_gc "Sidekiq" "$SIDEKIQ_PID"
    fi
    
    sleep 10  # Wait for GC to complete
    show_gc_stats
}

# Function to create GC monitoring script for Rails
create_gc_monitor_script() {
    cat > config/initializers/gc_monitor.rb << 'EOF'
# GC Monitor for Chatwoot
# Logs GC statistics and provides endpoints for manual GC

if Rails.env.development?
  # Enable GC profiling
  GC::Profiler.enable
  
  # Log GC stats periodically
  Thread.new do
    loop do
      sleep 300  # Every 5 minutes
      gc_stats = GC.stat
      Rails.logger.info "[GC Stats] Count: #{gc_stats[:count]}, Heap slots: #{gc_stats[:heap_live_slots]}, Memory: #{`ps -o rss= -p #{Process.pid}`.to_i / 1024}MB"
      
      # Force GC if memory is high
      memory_mb = `ps -o rss= -p #{Process.pid}`.to_i / 1024
      if memory_mb > (Rails.application.class.name.include?('Sidekiq') ? 250 : 350)
        Rails.logger.info "[GC] Triggering garbage collection due to high memory usage: #{memory_mb}MB"
        GC.start
      end
    end
  end
  
  # Handle USR1 signal for manual GC
  Signal.trap('USR1') do
    Rails.logger.info "[GC] Manual garbage collection triggered"
    before_memory = `ps -o rss= -p #{Process.pid}`.to_i / 1024
    GC.start
    after_memory = `ps -o rss= -p #{Process.pid}`.to_i / 1024
    reduction = before_memory - after_memory
    Rails.logger.info "[GC] Memory before: #{before_memory}MB, after: #{after_memory}MB, reduced: #{reduction}MB"
  end
end
EOF
    
    log "✅ Created GC monitor script at config/initializers/gc_monitor.rb"
}

# Main function
main() {
    case "${1:-monitor}" in
        "optimize")
            optimize_gc_env
            ;;
        "monitor")
            optimize_gc_env
            monitor_and_gc
            ;;
        "force")
            force_gc_all
            ;;
        "stats")
            show_gc_stats
            ;;
        "setup")
            create_gc_monitor_script
            log "✅ GC optimizer setup complete"
            ;;
        *)
            echo "Usage: $0 {optimize|monitor|force|stats|setup}"
            echo "  optimize - Set optimal GC environment variables"
            echo "  monitor  - Start continuous GC monitoring"
            echo "  force    - Force GC on all Ruby processes"
            echo "  stats    - Show current GC statistics"
            echo "  setup    - Create GC monitoring Rails initializer"
            exit 1
            ;;
    esac
}

# Handle signals
trap 'log "🛑 GC optimizer stopped"; exit 0' SIGTERM SIGINT

# Start
log "🚀 GC optimizer started with command: ${1:-monitor}"
main "$@"

#!/usr/bin/env bash

# Memory Dashboard for Chatwoot
# Real-time monitoring dashboard with alerts and recommendations

REFRESH_INTERVAL=${1:-5}  # Default 5 seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to get process memory
get_process_memory() {
    local pid="$1"
    ps -p "$pid" -o rss= 2>/dev/null | awk '{printf "%.0f", $1/1024}' || echo "0"
}

# Function to get process CPU
get_process_cpu() {
    local pid="$1"
    ps -p "$pid" -o %cpu= 2>/dev/null | awk '{printf "%.1f", $1}' || echo "0.0"
}

# Function to format memory with color
format_memory() {
    local mem="$1"
    local threshold="$2"
    
    if [ "$mem" -gt "$threshold" ]; then
        echo -e "${RED}${mem}MB${NC}"
    elif [ "$mem" -gt $((threshold * 80 / 100)) ]; then
        echo -e "${YELLOW}${mem}MB${NC}"
    else
        echo -e "${GREEN}${mem}MB${NC}"
    fi
}

# Function to format percentage with color
format_percentage() {
    local pct="$1"
    local threshold="$2"
    
    if [ "$pct" -gt "$threshold" ]; then
        echo -e "${RED}${pct}%${NC}"
    elif [ "$pct" -gt $((threshold * 80 / 100)) ]; then
        echo -e "${YELLOW}${pct}%${NC}"
    else
        echo -e "${GREEN}${pct}%${NC}"
    fi
}

# Function to show recommendations
show_recommendations() {
    local available_mem="$1"
    local swap_used="$2"
    local vite_mem="$3"
    local puma_mem="$4"
    local sidekiq_mem="$5"
    
    echo -e "\n${CYAN}=== Recommendations ===${NC}"
    
    if [ "$available_mem" -lt 300 ]; then
        echo -e "${RED}⚠️  Critical: Low memory ($available_mem MB available)${NC}"
        echo "   • Consider restarting VSCode or other memory-heavy applications"
        echo "   • Run: ./bin/process_manager to auto-restart processes"
    fi
    
    if [ "$swap_used" -gt 50 ]; then
        echo -e "${YELLOW}⚠️  Warning: High swap usage ($swap_used%)${NC}"
        echo "   • Consider increasing WSL2 memory allocation"
        echo "   • Check .wslconfig file in Windows user directory"
    fi
    
    if [ "$vite_mem" -gt 500 ]; then
        echo -e "${YELLOW}⚠️  Warning: Vite memory high ($vite_mem MB)${NC}"
        echo "   • Clear Vite cache: rm -rf node_modules/.vite"
        echo "   • Restart Vite: overmind restart vite"
    fi
    
    if [ "$puma_mem" -gt 300 ]; then
        echo -e "${YELLOW}⚠️  Warning: Puma memory high ($puma_mem MB)${NC}"
        echo "   • Trigger GC: pkill -USR1 -f 'puma.*chatwoot'"
        echo "   • Consider restarting: overmind restart backend"
    fi
    
    if [ "$sidekiq_mem" -gt 250 ]; then
        echo -e "${YELLOW}⚠️  Warning: Sidekiq memory high ($sidekiq_mem MB)${NC}"
        echo "   • Check job queue: bundle exec sidekiq-web"
        echo "   • Consider restarting: overmind restart worker"
    fi
}

# Main monitoring loop
monitor_dashboard() {
    while true; do
        clear
        echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${BLUE}║                           Chatwoot Memory Dashboard                          ║${NC}"
        echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
        echo -e "${PURPLE}Last updated: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
        echo ""
        
        # Get PIDs
        VITE_PID=$(pgrep -f "vite.*development" | head -1)
        PUMA_PID=$(pgrep -f "puma.*chatwoot" | head -1)
        SIDEKIQ_PID=$(pgrep -f "sidekiq.*chatwoot" | head -1)
        
        # System memory
        MEMORY_INFO=$(free -m | awk 'NR==2{printf "%.0f %.0f %.0f", $2, $3, $7}')
        TOTAL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f1)
        USED_MEM=$(echo $MEMORY_INFO | cut -d' ' -f2)
        AVAIL_MEM=$(echo $MEMORY_INFO | cut -d' ' -f3)
        
        # Swap usage
        SWAP_INFO=$(free -m | awk 'NR==3{if($2>0) printf "%.0f %.0f %.0f", $2, $3, $3/$2*100; else print "0 0 0"}')
        SWAP_TOTAL=$(echo $SWAP_INFO | cut -d' ' -f1)
        SWAP_USED_MB=$(echo $SWAP_INFO | cut -d' ' -f2)
        SWAP_USED_PCT=$(echo $SWAP_INFO | cut -d' ' -f3)
        
        # Process memory and CPU
        VITE_MEM=$(get_process_memory "$VITE_PID")
        VITE_CPU=$(get_process_cpu "$VITE_PID")
        PUMA_MEM=$(get_process_memory "$PUMA_PID")
        PUMA_CPU=$(get_process_cpu "$PUMA_PID")
        SIDEKIQ_MEM=$(get_process_memory "$SIDEKIQ_PID")
        SIDEKIQ_CPU=$(get_process_cpu "$SIDEKIQ_PID")
        
        # VSCode memory
        VSCODE_MEM=$(ps aux | grep -E "vscode-server" | grep -v grep | awk '{sum+=$6} END {printf "%.0f", sum/1024}')
        
        # Load average
        LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        
        # Display system overview
        echo -e "${CYAN}System Overview:${NC}"
        echo -e "  Memory: $(format_memory $USED_MEM 3000)/${TOTAL_MEM}MB used, $(format_memory $AVAIL_MEM 300) available"
        echo -e "  Swap:   $(format_memory $SWAP_USED_MB 1000)/${SWAP_TOTAL}MB used ($(format_percentage $SWAP_USED_PCT 50))"
        echo -e "  Load:   $LOAD"
        echo ""
        
        # Display process status
        echo -e "${CYAN}Process Status:${NC}"
        printf "  %-12s %-12s %-12s %-12s %-12s\n" "Process" "PID" "Memory" "CPU%" "Status"
        printf "  %-12s %-12s %-12s %-12s %-12s\n" "-------" "---" "------" "----" "------"
        
        if [ -n "$VITE_PID" ]; then
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Vite" "$VITE_PID" "$(format_memory $VITE_MEM 800)" "${VITE_CPU}%" "${GREEN}Running${NC}"
        else
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Vite" "N/A" "0MB" "0.0%" "${RED}Stopped${NC}"
        fi
        
        if [ -n "$PUMA_PID" ]; then
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Puma" "$PUMA_PID" "$(format_memory $PUMA_MEM 400)" "${PUMA_CPU}%" "${GREEN}Running${NC}"
        else
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Puma" "N/A" "0MB" "0.0%" "${RED}Stopped${NC}"
        fi
        
        if [ -n "$SIDEKIQ_PID" ]; then
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Sidekiq" "$SIDEKIQ_PID" "$(format_memory $SIDEKIQ_MEM 300)" "${SIDEKIQ_CPU}%" "${GREEN}Running${NC}"
        else
            printf "  %-12s %-12s %-12s %-12s %-12s\n" "Sidekiq" "N/A" "0MB" "0.0%" "${RED}Stopped${NC}"
        fi
        
        printf "  %-12s %-12s %-12s %-12s %-12s\n" "VSCode" "Multiple" "$(format_memory $VSCODE_MEM 2000)" "N/A" "${GREEN}Running${NC}"
        
        # Show recommendations
        show_recommendations "$AVAIL_MEM" "$SWAP_USED_PCT" "$VITE_MEM" "$PUMA_MEM" "$SIDEKIQ_MEM"
        
        echo ""
        echo -e "${BLUE}Press Ctrl+C to exit | Refresh every ${REFRESH_INTERVAL}s${NC}"
        
        sleep "$REFRESH_INTERVAL"
    done
}

# Handle signals
trap 'echo -e "\n${GREEN}Dashboard stopped${NC}"; exit 0' SIGTERM SIGINT

# Start dashboard
monitor_dashboard

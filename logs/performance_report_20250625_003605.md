# Chatwoot Performance Validation Report

**Generated:** Wed Jun 25 00:36:05 -03 2025

## System Overview
- **Total Memory:** 3.8Gi
- **Available Memory:** 1.7Gi
- **Swap Usage:** 49.9%
- **Load Average:**  0.34, 1.37, 1.64

## Process Status
| Process | PID | Memory | CPU |
|---------|-----|--------|-----|
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 9066 | 789.223MB | 32.8% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 8455 | 260.73MB | 13.2% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 22790 | 209.164MB | 3.5% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 9038 | 93.4453MB | 5.3% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 22789 | 67.9141MB | 2.2% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 8333 | 54.0781MB | 2.8% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 8499 | 42.8516MB | 1.0% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 9031 | 28.1094MB | 0.7% |
| /home/<USER>/.vscode-server/bin/18e3a1ec544e6907be1e944a94c496e302073435/node | 8346 | 22.2422MB | 0.1% |

## Performance Metrics
- **Backend Response Time:** 0.000236sN/A
- **Vite Response Time:** 0.000153sN/A

## Recommendations
[1;33m⚠️  Swap usage: 50% (moderate)[0m
[0;31m❌ Vite process not found[0m
[0;31m❌ Puma process not found[0m
[0;31m❌ Sidekiq process not found[0m
[0;31m❌ Backend response time: ms (too slow)[0m
[0;31m❌ Vite response time: ms (too slow)[0m
[0;31m❌ HTML title: Missing[0m
[0;31m❌ Vite integration: Missing[0m
[0;31m❌ JavaScript includes: Missing[0m

---
*Report generated by Chatwoot Performance Validator*
